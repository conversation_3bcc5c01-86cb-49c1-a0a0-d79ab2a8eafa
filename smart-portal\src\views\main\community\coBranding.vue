<template>
  <!-- 品牌联名 -->
  <div class="coBranding">
    <img-cover :maxHeight="false" :imgSrc="coverImg" :title="title"></img-cover>
    <!-- 第一屏 -->
    <div class="memon">
      <div class="container">
        <div class="left-content">
          <h2 class="left-content-title">Affiliate Program</h2>
          <p>
            Vector Optics was founded in 2007 with a mission to revolutionize
            the optical market for shooters. It is based in Shanghai, China. We
            started with two goals, first, to build the most valuable optics
            brand, and second, to share our success with our customers and
            supporters. The vision has led us to initiate our affiliate program.
            If you're interested in earning commissions, expanding your social
            media influence, and testing our products, you're the one we are
            looking for! Join our program and start earning commissions now!
          </p>
        </div>
        <m-image
          class="right-content"
          :pcSrc="affiliateImg"
          fit="cover"
        ></m-image>
      </div>
    </div>
    <!-- 第二屏 -->
    <div class="memtw">
      <div class="container">
        <div class="title">WHY JOIN</div>
        <div class="box">
          <div class="box-item">
            <img src="../../../assets/images/community/join01.png" alt="" />
            <div class="box-item-title">5% Commission</div>
            <div class="box-item-subtitle">
              Earn 5% commission for every sale you
            </div>
          </div>
          <div class="box-item">
            <img src="../../../assets/images/community/join02.png" alt="" />
            <div class="box-item-title">Monthly- Settlement</div>
            <div class="box-item-subtitle">
              Get your commission on a monthly basis
            </div>
          </div>
          <div class="box-item">
            <img src="../../../assets/images/community/join03.png" alt="" />
            <div class="box-item-title">Influence boost</div>
            <div class="box-item-subtitle">
              Increase the views, subscribes and likes on your social media
              accounts
            </div>
          </div>
          <div class="box-item">
            <img src="../../../assets/images/community/join04.png" alt="" />
            <div class="box-item-title">Expert Support</div>
            <div class="box-item-subtitle">
              Get 1:1 product support from our expert team. We are here to
              answer your questions and help you achieve success.
            </div>
          </div>
          <div class="box-item">
            <img src="../../../assets/images/community/join05.png" alt="" />
            <div class="box-item-title">Ads Material Support</div>
            <div class="box-item-subtitle">
              Get quality banners & text ads from Vector Optics.
            </div>
          </div>
          <div class="box-item">
            <img src="../../../assets/images/community/join06.png" alt="" />
            <div class="box-item-title">Exclusive Newsletters</div>
            <div class="box-item-subtitle">
              Receive regular newsletters to keep you informed
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 第三屏 -->
    <div class="memth">
      <div class="container">
        <div class="memth-header">To Be Our Affiliate， You are ......</div>
        <div class="memth-content">
          <div
            class="memth-content-item"
            v-for="(memthItem, memethIndex) in memthList"
          >
            <div class="memth-content-item-overlay">
              <div class="memth-content-item-number">
                {{ memthItem.number }}
              </div>
              <div class="memth-content-item-text">
                <p>{{ memthItem.title }}</p>
              </div>
            </div>
            <div class="memth-content-item-info">
              <div class="memth-content-item-info-number">
                {{ memthItem.number }}
              </div>
              <div class="memth-content-item-info-text">
                <p>{{ memthItem.title }}</p>
              </div>
            </div>
            <img :src="memthItem.img" alt="" />
          </div>
        </div>
      </div>
    </div>
    <!-- 第四屏 -->
    <div class="memfo">
      <div class="memfo-container">
        <div class="memfo-container-title">
          As an Affiliate, What Can You Do for Us?
        </div>
        <div class="memfo-container-content">
          <div class="memfo-container-content-item">
            <div class="memfo-container-content-item-header">01</div>
            <p class="memfo-container-content-item-text">
              Create qualified content of VE product on any social media
              platforms, and tag us or add us as a collaborator.
            </p>
          </div>
          <div class="memfo-container-content-item">
            <div class="memfo-container-content-item-header">02</div>
            <p class="memfo-container-content-item-text">
              Include the discount code in your posts to forward your followers
              to buy VE product.
            </p>
          </div>
          <div class="memfo-container-content-item">
            <div class="memfo-container-content-item-header">03</div>
            <p class="memfo-container-content-item-text">
              Provide us honest feedback & reviews on VE product, help us
              improve!
            </p>
          </div>
        </div>
      </div>
    </div>
    <div class="brandform">
      <div class="brandform-container">
        <div class="brandform-container-title">
          Fill in the Form to Join our Affiliate Program!
        </div>
        <div class="brandform-container-subtitle">
          Fill in the Form to Join our Affiliate Program!
        </div>
        <div class="brandform-container-content">
          <BaseForm ref="formRef" :model-value="joinForm" :rules="rules">
            <template #default="{ errors: formErrors }">
              <FormRow>
                <FormCol span="10">
                  <BaseFormItem
                    labelKey="community.joinForm.lastName"
                    required
                    :error="formErrors.lastName"
                  >
                    <m-input
                      :model-value="joinForm.lastName"
                      @update:model-value="
                        (val) => handleFieldUpdate('lastName', val)
                      "
                      theme="light2"
                      size="small"
                      maxlength="50"
                      :placeholder="t('common.warranty.lastNamePlaceholder')"
                    />
                  </BaseFormItem>
                </FormCol>
                <FormCol span="10">
                  <BaseFormItem
                    labelKey="community.joinForm.firstName"
                    required
                    :error="formErrors.firstName"
                  >
                    <m-input
                      :model-value="joinForm.firstName"
                      @update:model-value="
                        (val) => handleFieldUpdate('firstName', val)
                      "
                      theme="light2"
                      size="small"
                      type="email"
                      maxlength="100"
                      :placeholder="t('common.warranty.emailPlaceholder')"
                    />
                  </BaseFormItem>
                </FormCol>
                <FormCol span="12">
                  <BaseFormItem
                    labelKey="community.joinForm.birthDate"
                    required
                    :error="formErrors.birthDate"
                  >
                    <VueDatePicker
                      :model-value="joinForm.birthDate"
                      @update:model-value="
                        (val) => handleFieldUpdate('birthDate', val)
                      "
                      :placeholder="
                        t('common.warranty.purchaseDatePlaceholder')
                      "
                      format="yyyy-MM-dd"
                      auto-apply
                      :enable-time-picker="false"
                      :clearable="true"
                      :teleport="true"
                      class="warranty-date-picker"
                    />
                  </BaseFormItem>
                </FormCol>
                <FormCol span="24">
                  <BaseFormItem labelKey="community.joinForm.gender" required :error="formErrors.phone">
                    <m-radio
                      :model-value="joinForm.gender"
                      @update:model-value="
                        (val) => handleFieldUpdate('gender', val)
                      "
                      :options="genderOptions"
                    ></m-radio>
                  </BaseFormItem>
                </FormCol>
              </FormRow>
            </template>
          </BaseForm>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from "vue-i18n";
// 组件引入
import imgCover from "@/components/page/imgCover.vue";
import VueDatePicker from "@vuepic/vue-datepicker";
import "@vuepic/vue-datepicker/dist/main.css";
import BaseForm from "@/components/mCommon/form/BaseForm.vue";
import BaseFormItem from "@/components/mCommon/form/BaseFormItem.vue";
import FormRow from "@/components/mCommon/form/FormRow.vue";
import FormCol from "@/components/mCommon/form/FormCol.vue";
import MSelect from "@/components/mCommon/mSelect.vue";
import MRadio from "@/components/common/mRadio.vue";

// 静态资源
import coverImg from "@/assets/images/bg/page-cover.png";
import affiliateImg from "@/assets/images/test/affiliate-program.png";
import memth01 from "@/assets/images/community/memth01.png";
import memth02 from "@/assets/images/community/memth02.png";
import memth03 from "@/assets/images/community/memth03.png";
import memth04 from "@/assets/images/community/memth04.png";

const { t, locale } = useI18n();

const title = ref("documentTitle.coBranding");

const memthList = ref([
  {
    number: "01",
    title: "Shooting professionals/enthusiasts and shooting content creators",
    img: memth01,
  },
  {
    number: "02",
    title: "Shooting/hunting related content creators",
    img: memth02,
  },
  {
    number: "03",
    title: "Our customers or fans",
    img: memth03,
  },
  {
    number: "04",
    title: "Optics or related products professionals",
    img: memth04,
  },
]);

const joinForm = ref({
  firstName: "",
  lastName: "",
  birthDate: "",
  gender: "0",
});
const rules = ref({});

const genderOptions = ref([
  { label: "Male", value: "0" },
  { label: "Female", value: "1" },
]);

// 处理单个字段更新
const handleFieldUpdate = (field, value) => {
  joinForm.value[field] = value;
};
</script>

<style lang="scss">
@use "@/assets/styles/theme/mixin/font" as *;
@use "@/assets/styles/theme/mixin/flex" as *;
@use "@/assets/styles/theme/default/var/vueDatepicker.scss" as *;

// 在组件级别重新定义 Vue Datepicker 变量以确保覆盖
.warranty-date-picker {
  width: 100%;

  // 确保输入框样式与项目一致
  :deep(.dp__input) {
    height: var(--input-size-small);
    background-color: var(--color-background-light3);
  }
}
.memon {
  background-color: #fff;
  .container {
    width: 100%;
    display: flex;
    .left-content {
      width: 50%;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      padding: 1.65rem 1.5rem 1.2625rem 3rem;
      h2 {
        margin-bottom: 0.625rem;
        font-weight: 600;
        font-size: 0.5rem;
        color: #1d1d1d;
        // border: 0.025rem solid #1d1d1d;
      }
      p {
        font-weight: 400;
        font-size: 0.25rem;
        color: #737373;
        letter-spacing: 0;
        line-height: 0.375rem;
      }
    }
    .right-content {
      width: 50%;
    }
  }
}
.memtw {
  background: #eeeeee99;
  padding: 1rem 0;
  .container {
    width: 85%;
    max-width: 1440px;
    margin: 0 auto;
    .title {
      color: var(--color-font-primary-light);
      font-size: 0.35rem;
      line-height: var(--font-line-height-mini);
      font-weight: 600;
      font-family: Electrolize;
      text-align: center;
      position: relative;

      &::after {
        content: "";
        position: absolute;
        bottom: -0.1875rem;
        left: 50%;
        transform: translateX(-50%);
        width: var(--layout-spacing);
        height: var(--layout-size-dot);
        background-color: var(--color-primary);
      }
    }
    .box {
      margin-top: 0.75rem;
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      column-gap: 1.5rem;
      row-gap: 0.8125rem;
      flex-wrap: wrap;
      .box-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        img {
          width: 1.7rem;
          height: 1.7rem;
        }
        .box-item-title {
          font-family: "Electrolize";
          font-weight: 400;
          font-size: 0.25rem;
          color: #1d1d1d;
          line-height: 27px;
          text-align: center;
          margin-top: 0.45rem;
        }
        .box-item-subtitle {
          margin-top: 0.325rem;
          font-weight: 400;
          font-size: 0.225rem;
          color: #737373;
          letter-spacing: 0;
          text-align: center;
        }
      }
    }
  }
}
.memth {
  background-color: #111517;
  .container {
    width: 100%;
    .memth-header {
      padding-top: 0.95rem;
      padding-bottom: 0.75rem;
      font-family: "Electrolize";
      font-weight: 600;
      font-size: 0.5rem;
      color: #ffffff;
      letter-spacing: 0;
      text-align: center;
    }
    .memth-content {
      display: flex;
      .memth-content-item {
        flex: 1;
        position: relative;
        cursor: pointer;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
        .memth-content-item-overlay {
          opacity: 0;
          position: absolute;
          top: 20%;
          left: 5%;
          width: 90%;
          height: 60%;
          background: #eeaf00;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          z-index: 2;
          padding: 0 0.4625rem;
          transition: all 0.3s ease 0.1s;
          .memth-content-item-number {
            font-family: "Electrolize";
            font-weight: 700;
            font-size: 0.75rem;
            color: #1d1d1d;
            margin-bottom: 0.2rem;
            transition: all 0.3s ease 0.1s;
          }
          .memth-content-item-text {
            text-align: center;
            p {
              font-weight: 400;
              font-size: 0.225rem;
              color: #1d1d1d;
              line-height: 1.4;
              margin: 0;
            }
          }
        }
        .memth-content-item-info {
          position: absolute;
          top: 0%;
          left: 0%;
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: flex-end;
          align-items: center;
          .memth-content-item-info-number {
            opacity: 0.4;
            font-size: 0.75rem;
            color: #ffffff;
          }
          .memth-content-item-info-text {
            width: 100%;
            height: 1.65rem;
            background-color: #1d1d1d;
            text-align: center;
            font-weight: 400;
            font-size: 0.225rem;
            padding-top: 0.375rem;
            padding: 0.375rem 0.825rem 0;
          }
        }
        &:hover {
          .memth-content-item-overlay {
            opacity: 1;
          }
          .memth-content-item-info {
            opacity: 0;
          }
        }
      }
    }
  }
}
.memfo {
  padding: 1rem 0px;
  background-color: #fff;
  .memfo-container {
    width: 85%;
    max-width: 1440px;
    margin: 0 auto;
    .memfo-container-title {
      color: var(--color-font-primary-light);
      font-size: 0.35rem;
      line-height: var(--font-line-height-mini);
      font-weight: 600;
      font-family: Electrolize;
      text-align: center;
      position: relative;

      &::after {
        content: "";
        position: absolute;
        bottom: -0.1875rem;
        left: 50%;
        transform: translateX(-50%);
        width: var(--layout-spacing);
        height: var(--layout-size-dot);
        background-color: var(--color-primary);
      }
    }
    .memfo-container-content {
      margin-top: 0.5rem;
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      column-gap: 0.75rem;
      .memfo-container-content-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        padding: 0.5rem 0.25rem 0px;
        background: #eeeeee99;
        height: 3.85rem;
        .memfo-container-content-item-header {
          width: 0.75rem;
          height: 0.75rem;
          background: #1d1d1d;
          border-radius: 50%;
          font-family: "Electrolize";
          font-weight: 600;
          font-size: 0.35rem;
          color: #ffffff;
          text-align: center;
          line-height: 0.75rem;
        }
        .memfo-container-content-item-text {
          margin-top: 0.475rem;
          font-family: "Electrolize";
          font-weight: 400;
          font-size: 0.25rem;
          color: #1d1d1d;
          letter-spacing: 0;
          text-align: center;
          line-height: 0.375rem;
        }
      }
    }
  }
}
.brandform {
  background: #eeeeee99;
  padding: 1rem 0px;
  .brandform-container {
    width: 52%;
    margin: 0 auto;
    .brandform-container-title {
      color: var(--color-font-primary-light);
      font-size: 0.35rem;
      line-height: var(--font-line-height-mini);
      font-weight: 600;
      font-family: "Electrolize";
      text-align: center;
    }
    .brandform-container-subtitle {
      margin-top: 0.3625rem;
      color: #737373;
      font-size: 0.225rem;
      font-weight: 400;
      font-family: "Electrolize";
      text-align: center;
    }
    .brandform-container-content {
      margin-top: 0.4375rem;
      margin-bottom: 0.4375rem;
      max-height: 8rem;
      background-color: #fff;
      overflow-y: scroll;
      padding: 0.5rem 1rem;
    }
  }
}
</style>
